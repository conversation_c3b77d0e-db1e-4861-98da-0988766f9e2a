# 方案分组功能说明

## 概述

本功能为 `PlanServiceImpl.generateGridPlan` 方法添加了方案分组功能，能够根据问题类型将生成的方案按照固定的解决方式分类进行分组展示。

## 新增数据结构

### 1. PlanGroupVo - 方案分组视图对象

```java
public class PlanGroupVo {
    private String groupType;           // 分组类型/分类名称
    private String groupDescription;    // 分组类型的中文描述
    private List<Plan> plans;          // 该分组下的方案列表
    private Integer planCount;         // 该分组下的方案数量
}
```

### 2. PlanGroupResultVo - 方案分组结果视图对象

```java
public class PlanGroupResultVo {
    private Long problemId;                    // 问题ID
    private Integer categoryLevel2Code;        // 问题分类编码
    private String categoryDescription;        // 问题分类描述
    private List<PlanGroupVo> groups;         // 所有分组列表
    private Integer totalPlanCount;           // 总方案数量
    private Integer groupCount;               // 分组数量
}
```

## 问题类型与解决方式映射

| 问题类型 | 分类编码 | 固定解决方式分类 |
|---------|---------|----------------|
| 分段内配变数量不合理 | 1 | segKgType, contactType, bdzNewLineType |
| 大分支无联络 | 7 | contactType |
| 线路重过载 | 13 | runAdjustType, bdzNewLineType, contactType |
| 线路挂机配变过多 | 10 | runAdjustType, bdzNewLineType, contactType |
| 同母单联络 | 6 | replaceLayType, contactType |
| 单辐射无联络 | 3 | contactType, bdzNewLineType |

## 解决方式分类说明

| 分类代码 | 中文描述 |
|---------|---------|
| segKgType | 分段开关方案 |
| contactType | 联络方案 |
| bdzNewLineType | 变电站新出线方案 |
| runAdjustType | 运行调整方案 |
| replaceLayType | 替换间隔方案 |

## 新增方法

### 1. generateGridPlanWithGroups

```java
public PlanGroupResultVo generateGridPlanWithGroups(GeneratePlanBo generatePlanBo)
```

- **功能**: 生成网架方案并按类型分组
- **参数**: GeneratePlanBo - 方案生成参数
- **返回**: PlanGroupResultVo - 包含分组结构的方案结果

### 2. API端点

```
POST /plan/generatePlanWithGroups
```

- **请求体**: GeneratePlanBo
- **响应**: R<PlanGroupResultVo>

## 使用示例

### 1. 调用分组方案生成

```java
@Autowired
private IPlanService planService;

public void generateGroupedPlans() {
    GeneratePlanBo generatePlanBo = new GeneratePlanBo();
    generatePlanBo.setProblemId(1L);
    generatePlanBo.setToken("your-token");
    
    PlanGroupResultVo result = planService.generateGridPlanWithGroups(generatePlanBo);
    
    // 处理分组结果
    for (PlanGroupVo group : result.getGroups()) {
        System.out.println("分组: " + group.getGroupDescription());
        System.out.println("方案数量: " + group.getPlanCount());
        
        for (Plan plan : group.getPlans()) {
            System.out.println("  方案ID: " + plan.getId());
        }
    }
}
```

### 2. HTTP请求示例

```bash
curl -X POST http://localhost:8080/plan/generatePlanWithGroups \
  -H "Content-Type: application/json" \
  -d '{
    "problemId": 1,
    "token": "your-token"
  }'
```

### 3. 响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "problemId": 1,
    "categoryLevel2Code": 1,
    "categoryDescription": "分段内配变数量不合理",
    "groups": [
      {
        "groupType": "segKgType",
        "groupDescription": "分段开关方案",
        "plans": [
          {
            "id": 1,
            "planType": "segKgType",
            "budget": 50000,
            "advantage": "投资较少，实施简单"
          }
        ],
        "planCount": 1
      },
      {
        "groupType": "contactType",
        "groupDescription": "联络方案",
        "plans": [
          {
            "id": 2,
            "planType": "contactType",
            "budget": 120000,
            "advantage": "提高供电可靠性"
          }
        ],
        "planCount": 1
      },
      {
        "groupType": "bdzNewLineType",
        "groupDescription": "变电站新出线方案",
        "plans": [],
        "planCount": 0
      }
    ],
    "totalPlanCount": 2,
    "groupCount": 3
  }
}
```

## 特性说明

1. **固定分组结构**: 即使某个分类下没有方案，也会创建对应的空分组，确保分组结构的完整性
2. **灵活扩展**: 可以轻松添加新的问题类型和解决方式分类
3. **向下兼容**: 原有的 `generateGridPlan` 方法保持不变，不影响现有功能
4. **未分类处理**: 对于不属于固定分类的方案，会创建"其他方案"分组

## 测试

运行测试类 `PlanGroupingTest` 可以查看分组功能的示例用法：

```bash
mvn test -Dtest=PlanGroupingTest
```
