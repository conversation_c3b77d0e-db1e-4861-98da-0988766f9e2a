package com.ruoyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.vo.DeviceCurveVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.graph.Node;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.DeviceCurve;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;

import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.generatePlan.FeederPbMuchPlan;
import com.ruoyi.service.power.impl.PowerApiServiceImpl;
import com.ruoyi.service.power.impl.PowerApiServiceImpl2;
import com.ruoyi.service.problem.IProblemService;
import com.ruoyi.service.psm.impl.PsmSelectDeviceServiceImpl;
import com.ruoyi.service.singMap.impl.SingMapTopologyServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.constant.DeviceCurveType.*;
import static com.ruoyi.entity.cost.DeviceType.DKX;
import static com.ruoyi.graph.utils.NodeUtils.planOperateJsonToNodes;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/test")
@SaIgnore
public class Test {
    @Autowired
    DeviceCurve deviceCurve;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    FeederPbMuchPlan planTest;

    @Autowired
    SingMapTopologyServiceImpl singMapTopologyService;

    @Autowired
    PsmSelectDeviceServiceImpl psmSelectDeviceService;

    @Autowired
    PowerApiServiceImpl2 powerApiService;

    private final IProblemService iProblemService;


    @GetMapping("/testLoad/{feederId}/{defectTime}")
    public R<List<DeviceCurveVo>> testLoad(@PathVariable String feederId, @PathVariable @DateTimeFormat(pattern = "yyyyMMdd") Date defectTime) throws Exception {
        List<DeviceCurveVo> list = deviceCurve.selectTimeAndElectricCurrent(defectTime, feederId, DKX, Load + "," + ElectricCurrent_A_phs + "," + ElectricCurrent_A_phsA
                + "," + ElectricCurrent_A_phsB + "," + ElectricCurrent_A_phsC + "," + Power_TotW + "," + Power_TotVar);
        return R.ok(list);
    }

    @GetMapping("/test")
    public void test() {

        Double load = feederDeviceMapper.selectLoad("10DKX-180321");
        System.out.println(load);
    }

    @PostMapping("/selectDevice")
    public List<PBEntity> testNode(@RequestBody List<String> list) {
        return queryDeviceInfo.selectDevice(list);
    }

    @GetMapping("/jsonTONode")
    public void jsonTONode() throws JsonProcessingException {
        planOperateJsonToNodes("{\"devices\":[{\"coordinates\":[118.78669040241427,31.836526188485173],\"edge\":false,\"edgeIds\":[\"4ca2114b-71e0-4be2-b77a-2b62431e35b9\"],\"id\":\"65b3e4cd768a06deed5849782b015865b37e3e23eb\",\"name\":\"备用_1\",\"properties\":{\"parentHwgPsrId\":\"14000140969862\",\"contactFeederId\":\"10DKX-346081\",\"parentHwgPsrType\":\"zf07\",\"contactFeederName\":\"10k****125\",\"psrId\":\"65b3e4cd768a06deed5849782b015865b37e3e23eb\",\"psrType\":\"0307\",\"parentHwgName\":\"20kV迈瑞#2线#1环网柜\"},\"psrId\":\"65b3e4cd768a06deed5849782b015865b37e3e23eb\",\"psrType\":\"0307\",\"shapeKey\":\"shapePsrKey\",\"type\":\"typePsr\"},{\"coordinates\":[118.***********,31.836554205384],\"edge\":false,\"edgeIds\":[\"6261ce8f-48e3-4372-a811-cbadb3a283fa\",\"4ca2114b-71e0-4be2-b77a-2b62431e35b9\"],\"id\":\"4c5d824a-bb30-48f3-ba48-9420d33d2af3\",\"name\":\"杆塔\",\"properties\":{\"name\":\"杆塔\",\"type\":\"wlgt\"},\"psrType\":\"wlgt\",\"shapeKey\":\"wlgt\",\"type\":\"typeSelf\"},{\"coordinates\":[118.***********,31.836393006466],\"edge\":false,\"edgeIds\":[\"4af504ba-d548-4535-ae3e-0e63490f7644\",\"6261ce8f-48e3-4372-a811-cbadb3a283fa\"],\"id\":\"dcc2e906-44ec-4bb4-99b7-927ba92a0604\",\"name\":\"杆塔\",\"properties\":{\"name\":\"杆塔\",\"type\":\"wlgt\"},\"psrType\":\"wlgt\",\"shapeKey\":\"wlgt\",\"type\":\"typeSelf\"},{\"coordinates\":[118.78680425236386,31.836356110984227],\"edge\":false,\"edgeIds\":[\"4af504ba-d548-4535-ae3e-0e63490f7644\"],\"id\":\"1000004104888\",\"psrType\":\"wlgt\",\"shapeKey\":\"shapePsrKey\",\"type\":\"typePsr\"}],\"edges\":[{\"coordinates\":[[118.78669040241427,31.836526188485173],[118.***********,31.836554205384]],\"edge\":true,\"edgeIds\":[],\"id\":\"4ca2114b-71e0-4be2-b77a-2b62431e35b9\",\"lineType\":\"lineLinear\",\"name\":\"架空线\",\"properties\":{\"name\":\"架空线\",\"type\":\"feederJk\"},\"psrType\":\"dxd\",\"shapeKey\":\"feederJk\",\"sourceId\":\"65b3e4cd768a06deed5849782b015865b37e3e23eb\",\"targetId\":\"4c5d824a-bb30-48f3-ba48-9420d33d2af3\",\"type\":\"typeSelf\"},{\"coordinates\":[[118.***********,31.836554205384],[118.***********,31.836393006466]],\"edge\":true,\"edgeIds\":[],\"id\":\"6261ce8f-48e3-4372-a811-cbadb3a283fa\",\"lineType\":\"lineLinear\",\"name\":\"架空线\",\"properties\":{\"name\":\"架空线\",\"type\":\"feederJk\"},\"psrType\":\"dxd\",\"shapeKey\":\"feederJk\",\"sourceId\":\"4c5d824a-bb30-48f3-ba48-9420d33d2af3\",\"targetId\":\"dcc2e906-44ec-4bb4-99b7-927ba92a0604\",\"type\":\"typeSelf\"},{\"coordinates\":[[118.***********,31.836393006466],[118.78680425236386,31.836356110984227]],\"edge\":true,\"edgeIds\":[],\"id\":\"4af504ba-d548-4535-ae3e-0e63490f7644\",\"lineType\":\"lineLinear\",\"name\":\"架空线\",\"properties\":{\"name\":\"架空线\",\"type\":\"feederJk\"},\"psrType\":\"dxd\",\"shapeKey\":\"feederJk\",\"sourceId\":\"dcc2e906-44ec-4bb4-99b7-927ba92a0604\",\"targetId\":\"1000004104888\",\"type\":\"typeSelf\"}]}");
    }

    @GetMapping("/topoToNode/{feederId}")
    public void topoToNode(@PathVariable String feederId) throws IOException {
        singMapTopologyService.generateNodeList(feederId);
    }

    @GetMapping("/testApi")
    public void testApi() throws Exception {
        powerApiService.test();
    }

    @Autowired
    ISingMapService singMapService;

    /**
     * 返回需要导入的带线图和queryDeviceById入参
     */
    @GetMapping("/feederAndDeviceParams/{problemId}")
    public R<HashMap<String, Object>> feederAndDeviceParams(@PathVariable Long problemId) {

        ProblemVo problemVo = iProblemService.queryById(problemId);
        String feederId = problemVo.getFeederId();
        HashMap<String, Object> feederMap = new HashMap<String, Object>() {{
            put("devId", feederId);
            put("distribution", 0);
            put("devType", "dkx");
        }};

        HashMap<String, Object> result = new HashMap<>();
        result.put("singleUrl", "http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/amap-app-service/measurement/dms/getPic?psrId=" + feederId + "&psrType=dkx");

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        List<Node> nodeList = singAnalysis.getNodePath().getNodeList();

        // 获取需要queryDeviceById的节点
        nodeList = nodeList.stream().filter(n -> StringUtils.isNotBlank(n.getPsrId()) && StringUtils.isNotBlank(n.getPsrType()) && !DeviceConstants.VIRTUAL_TYPES.contains(n.getPsrType())).collect(Collectors.toList());

        List<HashMap<String, Object>> queryByIdsParams = nodeList.stream().map(node -> new HashMap<String, Object>() {{
            put("devId", node.getPsrId());
            put("distribution", 0);
            put("devType", node.getPsrType());
        }}).collect(Collectors.toList());
        queryByIdsParams.add(0, feederMap);

        result.put("queryByIdsParams", queryByIdsParams);
        return R.ok(result);
    }


}
