package com.ruoyi.controller.map;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.calc.FeederTransferCapVo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.service.map.IMapService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/map")
@SaIgnore
public class MapController extends BaseController {
    @Autowired
    IMapService mapService;


    /**
     * 查询一个线路指定范围内的线路
     */
    @PostMapping("/feederRangeQuery")
    public R<List<NeedFeederVo>> feederRangeQuery(@RequestBody FeederRangeQueryBo feederRangeQueryBo) {
        return R.ok(mapService.feederRangeQuery(feederRangeQueryBo));
    }


    /**
     * 根据杆塔id查询两端导线
     */
    @GetMapping("/selectWireEnd/{psrId}")
    public R<List<WireEndVo>> selectWireEnd(@PathVariable String psrId) {
        return R.ok(mapService.selectWireEnd(psrId));
    }

    /**
     * id和设备类型判断他的真正psrId
     */
    @GetMapping("/selectPsrId/{psrId}/{type}")
    public R<String> selectPsrId(@PathVariable String psrId, @PathVariable String type) {
        return R.ok("200", mapService.selectPsrId(psrId, type));
    }

    /**
     * 查询运方调整转供路径
     */
    @GetMapping("/transferSupply/{feederId}/{closeId}/{openId}")
    public R<List<NodeVo>> transferSupply(@PathVariable String feederId, @PathVariable String closeId, @PathVariable String openId) {
        return R.ok(mapService.transferSupply(feederId, closeId, openId));
    }

    /**
     * 根据线路ID获取联络开关信息列表
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    @GetMapping("/contactSwitchInfo/{feederId}")
    public R<List<ContactSwitchInfoVo>> getContactSwitchInfoByFeederId(@PathVariable String feederId) {
        List<ContactSwitchInfoVo> result = mapService.getContactSwitchInfo(feederId);
        return R.ok(result);
    }

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    @GetMapping("/calculateLoadRateChange/{feederId}/{closeId}/{openId}")
    public R<FeederTransferCapVo> calculateLoadRateChange(@PathVariable String feederId, @PathVariable String closeId, @PathVariable String openId) {
        FeederTransferCap feederTransferCap = mapService.calculateLoadRateChange(feederId, closeId, openId);
        return R.ok(feederTransferCap.toFTrVo());
    }

    /**
     * 查询附近线路展示信息
     * 根据线路ID和半径查询附近线路的详细信息，包括ID、名称、负载率、是否重过载、所属母线、所属隔离开关
     *
     * @param feederId 线路ID
     * @param radius   查询半径（米）
     * @return 附近线路信息列表
     */
    @GetMapping("/nearbyLines/{feederId}")
    public R<List<NearbyLineInfoVo>> getNearbyLinesInfo(@PathVariable String feederId, @RequestParam Double radius) {
        List<NearbyLineInfoVo> result = mapService.getNearbyLinesInfo(feederId, radius);
        return R.ok(result);
    }

    /**
     * 查询附近变电站信息
     *  （ID、名称、剩余间隔、容量）
     * @param feederId 线路ID
     * @return 附近变电站信息列表
     */
    @GetMapping("/nearbySubstations/{feederId}")
    public R<List<NearbySubstationInfoVo>> queryNearbySubstations(@PathVariable String feederId) {
        List<NearbySubstationInfoVo> result = mapService.queryNearbySubstations(feederId);
        return R.ok(result);
    }

    /**
     * 查询附近变电站 调用内网接口实现
     * （ID、名称、剩余间隔、容量）
     * @param feederId 线路ID
     * @return 附近变电站信息列表
     */
    @GetMapping("/nearbySubstationsByNet/{feederId}/{bufferRadius}")
    public R<List<NearbySubstationInfoVo>> queryNearbySubstationsByNet(@PathVariable String feederId,@PathVariable double bufferRadius) {
        List<NearbySubstationInfoVo> result = mapService.queryNearbySubstationsByNet(feederId,bufferRadius);
        return R.ok(result);
    }

    /**
     * 查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     */
    @GetMapping("/nearbyDevices/{feederId}/{bufferRadius}")
    public R<List<NearbyDeviceInfoVo>> queryNearbyDevices(@PathVariable String feederId,@PathVariable double bufferRadius) {
        List<NearbyDeviceInfoVo> result = mapService.queryNearbyDevices(feederId,bufferRadius);
        return R.ok(result);
    }

    /**
     * 根据一个坐标点查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     */
    @GetMapping("/nearbyDevicesByPoint/{lng}/{lat}/{bufferRadius}")
    public R<List<NearbyDeviceInfoVo>> queryNearbyDevicesByPoint(@PathVariable double lng,@PathVariable double lat,@PathVariable double bufferRadius) {
        List<NearbyDeviceInfoVo> result = mapService.queryNearbyDevicesByPoint(lng,lat,bufferRadius);
        return R.ok(result);
    }

}
