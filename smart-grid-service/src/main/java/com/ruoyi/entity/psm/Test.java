package com.ruoyi.entity.psm;

import cn.hutool.core.map.MapUtil;
import com.ruoyi.entity.znap.ContactFeederKg;

import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;

public class Test {
    public static void main(String[] args) throws IOException {
//        List<Map<String, Object>> mapList = new ArrayList<>();
//        Map<String, Object> parm = new HashMap<>();
//        parm.put("classId", "300");
//        parm.put("distribution", "0");
//        parm.put("provinceId", "ff80808149f52e24014a039871840007");
//        parm.put("psrId", "52d34efa-b994-4803-9333-6a429db16e4d");
//        parm.put("psrType", "dkx");
//        mapList.add(parm);
//        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(parm.size(), mapList);
//        AmapSdkCommon.AmapRspModel<List> baseInfo = AmapSdkCommon.getBaseInfo(parm);
//
//        System.out.println("baseInfo = " + baseInfo.getResult());

//        Map<String,String> parm =new HashMap<>();
//        parm.put("psrId","10DKX-445025");
//        parm.put("psrType","dkx");
//        AmapSdkCommon.LiaisonInfo<ContactFeederKg> liaisonInfoById = AmapSdkCommon.getLiaisonInfoById(parm);
//        System.out.println("liaisonInfoById = " + liaisonInfoById);


//        OccaAnalysisUtil.BaseResponse<OccaAnalysisUtil.LineResult> queryLineInfo = OccaAnalysisUtil.queryLineInfo("040832010000001709");
//        OccaAnalysisUtil.BaseResponse<OccaAnalysisUtil.SubstationResult> substationBasicTopo = OccaAnalysisUtil.substationBasicTopo("01123201000237");
//        System.out.println("substationBasicTopo = " + substationBasicTopo);
//
//        System.out.println("s = " + queryLineInfo);

        List<Map<String,Object>> params = new ArrayList<>();
        params.add(MapUtil.<String, Object>builder()
                .put("psrId", "10197")
                .put("psrType", "zf01")
                .put("distribution", 0).build());
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(params.size(), params).attrName(Arrays.asList("psrId", "psrType", "coordinate","psrName"));

        List<AmapSdkCommon.DeviceRspModel<AmapSdkCommon.DeviceInfo>> deviceRspModels = AmapSdkCommon.queryDeviceById(powerGridFilter);



        System.out.println("mapStream = " + deviceRspModels);

    }
}
