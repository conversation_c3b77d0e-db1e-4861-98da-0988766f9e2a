package com.ruoyi.service.map.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.MercatorLineCircleIntersection;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.DeviceCoords;
import com.ruoyi.entity.map.PathSegment;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.TopoTraceReal;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.znap.BayQueryMapper;
import com.ruoyi.mapper.znap.ConDmsCabinetMapper;
import com.ruoyi.mapper.znap.TpLinkCbMapper;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.service.map.INearbyQueryService;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.generatePlan.BaseGeneratePlan;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.GeoDistanceCalculator;
import com.ruoyi.util.ListUtils;
import com.ruoyi.vo.BusbarSwitchVo;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.util.map.GisUtils;
import com.ruoyi.util.map.PathSegmentationUtil;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.entity.psm.AmapSdkCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.constant.DeviceConstants.*;
import static com.ruoyi.entity.cost.DeviceType.*;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestFeederAllDevice;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestPoints;
import static com.ruoyi.util.map.LineDistanceQuery.selectLine;
import static com.ruoyi.util.map.MapAPICall.extractPolylines;
import static com.ruoyi.util.map.MapAPICall.sendGetRequest;
import static com.ruoyi.util.map.TowerCoordinateGenerator.generateTowerCoordinates;
import static java.lang.Thread.sleep;

@Service
@Slf4j
public class MapServiceImpl implements IMapService {
    @Value("${mapPath.path1}")
    private String path1;

    @Value("${mapPath.path2}")
    private String path2;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Value("${spring.profiles.default}")
    private String activeProfile = "";

    @Autowired
    private GisUtils gisUtils;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ExecutorService executorService; // 注入自定义线程池

    @Autowired
    private KgMapper kgMapper; // 注入自定义线程池

    @Autowired
    ISingMapService singMapService;

    @Autowired
    ContactHandleService contactHandleService;

    @Resource
    TpLinkCbMapper tpLinkCbMapper;

    @Resource
    IConditionService iConditionService;

    @Resource
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    IBayQueryService bayQueryService;

    @Resource
    private DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Resource
    private BayQueryMapper bayQueryMapper;

    @Resource
    private ConDmsCabinetMapper conDmsCabinetMapper;

    @Autowired
    private INearbyQueryService nearbyQueryService;

    @Value("${bdz-handler.mode}")
    private Boolean bdzHandlerMode;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    @Autowired
    private DeviceStationBreakerMapper deviceStationBreakerMapper;


    /**
     * 计算两点最优路劲
     *
     * @param start 起点坐标集合
     * @param end   结束坐标点集合
     * @param token 思级地图token
     * @return
     */
    public List<CalcRouteVo> calculateRouteAsync(List<double[]> start, List<double[]> end, String token) {
        //判断入参是否为空
        if (start == null || end == null || token == null) {
            return null;
        }

        List<CompletableFuture<CalcRouteVo>> futures = new ArrayList<>();
        for (int i = 0; i < start.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用
            // TODO 担心同时调用多个接口会封掉IP 我们这里加延时一下
            try {
                sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            futures.add(CompletableFuture.supplyAsync(() -> mapRoute(start.get(index), end.get(index), token)));
        }
        // 等待所有future完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        // 获取所有结果
        List<CalcRouteVo> results = allFutures.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList())).join();
        return results;
    }

    /***
     * 计算路劲方法
     * @param strPoint 起点坐标
     * @param endPoint 重点坐标
     * @param token   思级地图token
     * @return
     */

    public CalcRouteVo mapRoute(double[] strPoint, double[] endPoint, String token) {
        //判断入参是否为空
        if (strPoint == null || strPoint.length < 2 || endPoint == null || endPoint.length < 2 || StringUtils.isEmpty(token)) {
            throw new IllegalArgumentException("起点或终点坐标格式错误或者token异常");
        }

        String str = Arrays.toString(strPoint);  // 输出："[1.1, 2.2]"
        String end = Arrays.toString(endPoint);  // 输出："[1.1, 2.2]"
        // 如果需要去掉方括号和空格：
        str = str.replaceAll("[\\[\\]\\s]", "");
        end = end.replaceAll("[\\[\\]\\s]", "");
        String apiUrl = path1 + str + "&" + path2 + end;

        List<Node> nodeList = new ArrayList<>();
        List<double[]> lngLatList = new ArrayList<>();
        Double totalLength = 0.0;

        try {
            // TODO 查询路径规划接口
            RouteVo polylines = new RouteVo();
            if ("dev".equals(activeProfile)) {
                //获取spring激活的配置文件
                String response = sendGetRequest(apiUrl, token);
                JSONObject root = JSON.parseObject(response);
                Object codeObj = root.get("code");
                // 如果出现思级地图访问异常，则直接将两个点变成一个路径生成杆塔
                if (codeObj != null && !codeObj.equals("200")) {
                    List<GHPoint> ghPoints = new ArrayList<>();
                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
                    polylines.setRouteList(ghPoints);
                } else {
                    //解析路径坐标集合
                    log.warn("地图API响应为空，生成默认两点一线路径");
                    polylines = extractPolylines(response);
                }
            } else {
                Map<String, String> parm = new HashMap<>();
                parm.put("origin", str);
                parm.put("destination", end);
                // 解析路径坐标集合
                String commonGis = gisUtils.commonGis(parm);
                if (StringUtils.isEmpty(commonGis)) {
                    List<GHPoint> ghPoints = new ArrayList<>();
                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
                    polylines.setRouteList(ghPoints);
                } else {
                    polylines = extractPolylines(commonGis);
                }
            }

            List<GHPoint> routeList = polylines.getRouteList();
            routeList.forEach(ghPoint -> {
                double[] temp = {ghPoint.getLon(), ghPoint.getLat()};
                lngLatList.add(temp);
            });
            //计算长度
            totalLength = countLength(routeList);
            //分析杆塔
            List<GHPoint> gtList = generateTowerCoordinates(polylines.getRouteList());

            //判断起点和终点是否和生产的路径对应上，如果对不上则手动添加起始点和终点
            double[] routeStrPoint = {polylines.getRouteList().get(0).getLon(), polylines.getRouteList().get(0).getLat()};
            double[] routeEndPoint = {polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLon(), polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLat()};
            if (routeStrPoint[0] != strPoint[0] || routeStrPoint[1] != strPoint[1]) {
                GHPoint point = new GHPoint(strPoint[1], strPoint[0]);
                polylines.getRouteList().add(0, point);
                gtList.add(0, point);
            }
            if (routeEndPoint[0] != endPoint[0] || routeEndPoint[1] != endPoint[1]) {
                GHPoint point = new GHPoint(endPoint[1], endPoint[0]);
                polylines.getRouteList().add(point);
                gtList.add(point);
            }

            // 将路径和杆塔转化成后续node需要的list
            PathSegmentationUtil pathSegmentationUtil = new PathSegmentationUtil();
            List<PathSegment> list = pathSegmentationUtil.segmentRouteByTowers(polylines.getRouteList(), gtList);


            // 生产nodeList
            int count = 0;
            for (PathSegment pathSegment : list) {
                if (count == 0) {
                    Node node1 = createDevice(pathSegment.getStartTower(), "wlgt", "杆塔");
                    nodeList.add(node1);
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                } else {
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                }
                count++;
            }
            int nodeCount = 0;
            for (Node node : nodeList) {
                if (node.getPsrType().equals("wlgt")) {
                    if (nodeCount == 0) {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        nodeCount = nodeCount + 1;
                        continue;
                    }
                    if (nodeCount == nodeList.size() - 1) {
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    } else {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    }
                }
                nodeCount = nodeCount + 1;
            }
        } catch (Exception e) {
            return null;
        }
        HashMap<String, Object> objectHashMap = new HashMap<>();

        objectHashMap.put("length", totalLength);
        objectHashMap.put("contactNodeList", nodeList);
        return new CalcRouteVo(totalLength, nodeList, lngLatList);
    }


    /**
     * 查询一条线附近的线,根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo
     * @return
     */
    @Override
    public List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo) {
        return nearbyQueryService.feederRangeQuery(feederRangeQueryBo);
    }

    /**
     * 根据杆塔id查询两端导线
     */
    @Override
    public List<WireEndVo> selectWireEnd(String psrId) {
        List<WireEndVo> wireEndVoList = getWireEndVos(psrId);

        if (CollectionUtils.isEmpty(wireEndVoList)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return getWireEndVos(deviceRunTower.getPsrId());
            }
        }
        return wireEndVoList;
    }


    /**
     * id和设备类型判断他的真正psrId
     */
    @Override
    public String selectPsrId(String psrId, String psrType) {
        if (psrType.equals(WLGT)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return deviceRunTower.getPsrId();
            }
            return psrId;
        }
        return null;
    }

    /**
     * 查询运方调整转供路径
     * closeId 联络开关
     * openId  主干开关
     */
    @Override
    public List<NodeVo> transferSupply(String feederId, String closeId, String openId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);

        NodePath nodePath = singAnalysis.getNodePath();

        //找寻联络开关所在的节点分支
        List<Node> closeNodeList = nodePath.getAllNodePaths().stream().filter(path -> path.stream().filter(node -> node.getPsrId() != null) // 跳过psrId为空的节点
                .anyMatch(node -> node.getPsrId().equals(closeId))).findFirst().orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(closeNodeList)) {
            return null;
        }

        //闭合的开关
        Node contactNode = nodePath.getNodeByPsrId(closeId);
        //打开的开关
        Node fenNode = nodePath.getNodeByPsrId(openId);
        //上一个节点
        int index = closeNodeList.indexOf(contactNode) - 1;
        if (index < 0) {
            return null;
        }
        List<Node> nodes = contactHandleService.powerSupplyNodes(contactNode, closeNodeList.get(index), fenNode);
        List<NodeVo> nodeVoList = NodeUtils.toNodeVos(nodes);
        return nodeVoList;
    }

    /**
     * 查询杆塔关联的架空线就或者导线
     *
     * @param psrId
     * @return
     */
    private List<WireEndVo> getWireEndVos(String psrId) {
        //查询杆塔相关联的架空线
        LambdaQueryWrapper<DeviceFeederJk> jkLambdaQueryWrapper = new LambdaQueryWrapper<>();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStartPole, psrId);
        jkLambdaQueryWrapper.or();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStopPole, psrId);
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectList(jkLambdaQueryWrapper);

        List<WireEndVo> wireEndVoList = new ArrayList<>();
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederJkList)) {
            for (DeviceFeederJk deviceFeederJk : deviceFeederJkList) {
                wireEndVoList.add(new WireEndVo(deviceFeederJk.getPsrId(), deviceFeederJk.getName(), JK));
            }
        }

        //查询杆塔相关联的导线段
        LambdaQueryWrapper<DeviceFeederCable> cableLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getStartPosition, psrId);
        cableLambdaQueryWrapper.or();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getEndPosition, psrId);
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectList(cableLambdaQueryWrapper);
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederCableList)) {
            for (DeviceFeederCable deviceFeederCable : deviceFeederCableList) {
                wireEndVoList.add(new WireEndVo(deviceFeederCable.getPsrId(), deviceFeederCable.getName(), CABLE));
            }
        }
        return wireEndVoList;
    }

    /**
     * 快捷查询附近线路（从是数据库最近的线路表查询的）
     *
     * @param feederId
     * @param radius
     * @return
     */
    public List<DeviceFeeder> selectNearFeeder(String feederId, Double radius) {
        return nearbyQueryService.selectNearFeeder(feederId, radius);
    }

    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo 查询条件实体
     * @param deviceFeederList   已知的附近线
     * @return
     */
    public List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList) {
        return nearbyQueryService.selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
    }


    /**
     * 线找线（通过一条线找另一条线最近的点）
     *
     * @param doubleArrays 目标点坐标集合
     * @param num          最近几条的数量
     * @param filteredList 线路集合
     * @return
     */
    public List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList) {
        return nearbyQueryService.pointByParallel(doubleArrays, num, filteredList);
    }

    /**
     * 计算路径长度
     *
     * @param ghPoints
     * @return
     */
    public static Double countLength(List<GHPoint> ghPoints) {
        // 创建GeometryFactory实例（使用默认精度）
        GeometryFactory factory = new GeometryFactory();

        // 方式2: 使用ArrayList动态批量添加
        List<Coordinate> coordList = new ArrayList<>();
        for (GHPoint ghPoint : ghPoints) {
            Coordinate coordinate = new Coordinate();
            coordinate.setY(ghPoint.getLat());
            coordinate.setX(ghPoint.getLon());
            coordList.add(coordinate);
        }

        Coordinate[] coords = coordList.toArray(new Coordinate[0]);

        // 创建LineString对象
        org.locationtech.jts.geom.LineString lineString = factory.createLineString(coords);
        lineString.setSRID(4326);
        // 计算长度（单位取决于坐标系，例如经纬度坐标系下单位是度，需转换为米）
        double length = haversineLength(lineString);
        return length;
    }

    public static double haversineLength(org.locationtech.jts.geom.LineString line) {
        final double R = 6371000; // 地球半径（米）
        double totalDistance = 0.0;

        for (int i = 0; i < line.getNumPoints() - 1; i++) {
            Coordinate c1 = line.getCoordinateN(i);
            Coordinate c2 = line.getCoordinateN(i + 1);

            double dLat = Math.toRadians(c2.y - c1.y);
            double dLon = Math.toRadians(c2.x - c1.x);
            double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(Math.toRadians(c1.y)) * Math.cos(Math.toRadians(c2.y)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            totalDistance += R * c;
        }
        return totalDistance;
    }

    /**
     * 计算杆塔node
     *
     * @param ghPoint
     * @param type
     * @param name
     * @return
     */
    private static Node createDevice(GHPoint ghPoint, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setGeometry(nodeFactory.createDevice(node.getId(), ghPoint.getLon(), ghPoint.getLat()).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setShapeKey(NodeConstants.SHAPE_KEY_WLGT);

        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_WLGT);
            put("name", name);
        }});
        return node;
    }

    /**
     * 计算线路node
     *
     * @param pathSegment
     * @param type
     * @param name
     * @return
     */
    private static Node createEdge(PathSegment pathSegment, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setEdge(true);
        double[][] coords = {{pathSegment.getStartTower().getLon(), pathSegment.getStartTower().getLat()}, {pathSegment.getEndTower().getLon(), pathSegment.getEndTower().getLat()}};
        node.setGeometry(nodeFactory.createEdge(node.getId(), coords).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setLineType(NodeConstants.LINE_TYPE_LINEAR);
        node.setShapeKey(NodeConstants.SHAPE_KEY_FEEDER_JK);
        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_FEEDER_JK);
            put("name", name);
        }});
        return node;
    }

//    /**
//     * 将查询的最近线路重新封装成NeedFeederVo格式
//     *
//     * @param feederList
//     * @return
//     */
//    public List<NeedFeederVo> setFeeder(List<DeviceFeeder> feederList) {
//        List<NeedFeederVo> feederVoList = new ArrayList<>();
//        for (DeviceFeeder deviceFeeder : feederList) {
//            NeedFeederVo feederVo = new NeedFeederVo();
//            feederVo.setPsrId(deviceFeeder.getPsrId());
//            feederVo.setDeviceId(deviceFeeder.getDeviceId());
//            feederVo.setPoint1(deviceFeeder.getPoint1());
//            feederVo.setPoint2(deviceFeeder.getPoint2());
//            feederVo.setRecentlyPoint(deviceFeeder.getRecentlyPoint());
//            feederVo.setGeoCoordinateList(deviceFeeder.getGeoCoordinateList());
//        }
//        return feederVoList;
//    }

    /**
     * 获取联络开关信息列表
     * 从拓扑关系中获取联络开关信息，包括联络开关名称、联络线路名称、线路负载率、能否专供等数据
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    @Override
    public List<ContactSwitchInfoVo> getContactSwitchInfo(String feederId) {
        if (StringUtils.isEmpty(feederId)) {
            log.warn("输入的线路ID为空");
            return Collections.emptyList();
        }
        try {
            log.info("开始查询联络开关信息，线路ID: {}", feederId);
            // 获取单线图分析结果
            SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
            if (singAnalysis == null) {
                log.warn("未获取到线路{}的单线图分析结果", feederId);
                return Collections.emptyList();
            }
            // 获取拓扑信息
            ZnapTopology topology = singAnalysis.getTopologyMap();
            NodePath nodePath = singAnalysis.getNodePath();
            List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(nodePath);
            //  获取最大负荷
            double maxLoad = contactHandleService.getMaxFeederLoad();
            // 转换联络开关信息
            return buildContactSwitchInfo(nodePath, contactFeederNts, maxLoad);
        } catch (Exception e) {
            log.error("查询联络开关信息失败，线路ID: {}, 异常: {}", feederId, e.getMessage());
            throw new RuntimeException("查询联络开关信息失败", e);
        }
    }

    /**
     * 构建联络开关信息列表
     */
    private List<ContactSwitchInfoVo> buildContactSwitchInfo(NodePath nodePath, List<FeederNtVo> contactFeederNts, double maxLoad) {
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();
        List<Node> contactKgNodes = nodePath.getContactKgNodes();
        Map<String, Node> contactKgNodeMap = contactKgNodes.stream().collect(Collectors.toMap(Node::getPsrId, d -> d));
        if (CollectionUtils.isEmpty(contactFeederKgs) || CollectionUtils.isEmpty(contactFeederNts)) {
            return Collections.emptyList();
        }
        try {
            // 构建馈线NT映射
            Map<String, FeederNtVo> feederNtMap = contactFeederNts.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, Function.identity(), (v1, v2) -> v1));
            for (ContactFeederKg contactFeederKg : contactFeederKgs) {
                String kgPsrId = contactFeederKg.getKgPsrId();
                Node node = contactKgNodeMap.get(kgPsrId);
                if (node != null) {
                    contactFeederKg.setKgPsrName(node.getPsrName());
                }
                String feederPsrId = contactFeederKg.getFeederPsrId();
                FeederNtVo feederNtVo = feederNtMap.get(feederPsrId);
                if (feederNtVo != null) {
                    contactFeederKg.setFeederPsrName(feederNtVo.getName());
                }
            }
            // 转换联络开关信息
            return contactFeederKgs.stream().map(contactKg -> convertToSwitchInfo(contactKg, feederNtMap.get(contactKg.getFeederPsrId()), maxLoad)).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("构建联络开关信息失败: {}", e.getMessage());
            throw new RuntimeException("构建联络开关信息失败", e);
        }
    }

    /**
     * 转换单个联络开关信息
     */
    private ContactSwitchInfoVo convertToSwitchInfo(ContactFeederKg contactKg, FeederNtVo feederNtVo, double maxLoad) {
        if (contactKg == null || feederNtVo == null) {
            return null;
        }
        try {
            ContactSwitchInfoVo switchInfo = new ContactSwitchInfoVo();
            // 设置开关信息
            switchInfo.setSwitchId(contactKg.getKgPsrId());
            switchInfo.setSwitchName(contactKg.getKgPsrName());
            switchInfo.setSwitchType(contactKg.getKgPsrType());

            // 设置联络线路信息
            switchInfo.setContactLineId(contactKg.getFeederPsrId());
            switchInfo.setContactLineName(contactKg.getFeederPsrName());

            // 设置负荷率
            double loadRate = feederNtVo.getHisMaxLoadRate();
            switchInfo.setLoadRate(feederNtVo.getHisMaxLoadRateStr());
            switchInfo.setCanTransfer(loadRate < maxLoad);

            return switchInfo;
        } catch (Exception e) {
            log.error("转换联络开关信息失败, 开关ID: {}, 异常: {}", contactKg.getKgPsrId(), e.getMessage());
            return null;
        }
    }

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    @Override
    public FeederTransferCap calculateLoadRateChange(String feederId, String closeId, String openId) {
        if (StringUtils.isEmpty(feederId) || StringUtils.isEmpty(closeId) || StringUtils.isEmpty(openId)) {
            log.warn("输入参数不能为空: feederId={}, closeId={}, openId={}", feederId, closeId, openId);
            return null;
        }

        try {
            log.info("开始计算负载率变化，源线路ID: {}, 联络开关ID: {}, 主干开关ID: {}", feederId, closeId, openId);

            // 2. 获取单线图分析结果以获取拓扑信息
            SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
            if (singAnalysis == null) {
                log.warn("未获取到线路{}的单线图分析结果", feederId);
                return null;
            }

            NodePath nodePath = singAnalysis.getNodePath();
            List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();

            Node heNode = nodePath.getNodeByPsrId(closeId);
            Node fenNode = nodePath.getNodeByPsrId(openId);

            // =========================== 查找专供路径 =======================
            List<Node> tfrPaths = contactHandleService.powerSupplyNodes(heNode, fenNode, nodePath);
            FeederNtVo mainFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);
            List<Node> pbList = tfrPaths.stream().filter(Node::isPb).collect(Collectors.toList());

            // 配变节点的容量加工
            baseGeneratePlan.processPbNodeCap(pbList);

            // =========================== 查找关联联络线路 =======================
            // 查找合闸开关关联的联络线
            ContactFeederKg contactFeederKg = ListUtils.findFirst(contactFeederKgs, (d) -> StringUtils.equals(d.getKgPsrId(), heNode.getPsrId()));
            FeederNtVo contactFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(contactFeederKg.getFeederPsrId());

            // =========================== 计算转供负载率 =======================
            return contactHandleService.calcFeederTransferCap(mainFeederNt, contactFeederNt, pbList);

        } catch (Exception e) {
            log.error("计算负载率变化失败，源线路ID: {}, 联络开关ID: {}, 主干开关ID: {}, 异常: {}", feederId, closeId, openId, e.getMessage(), e);
            throw new RuntimeException("计算负载率变化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询附近线路展示信息
     * 根据线路ID和半径查询附近线路的详细信息，包括ID、名称、负载率、是否重过载、所属母线、所属隔离开关
     *
     * @param feederId 线路ID
     * @param radius   查询半径（米）
     * @return 附近线路信息列表
     */
    @Override
    public List<NearbyLineInfoVo> getNearbyLinesInfo(String feederId, Double radius) {
        if (StringUtils.isEmpty(feederId)) {
            log.warn("线路ID不能为空");
            return new ArrayList<>();
        }

        if (radius == null || radius <= 0) {
            log.warn("查询半径必须大于0");
            return new ArrayList<>();
        }

        try {
            log.info("开始查询附近线路信息，线路ID: {}, 半径: {}米", feederId, radius);
            FeederRangeQueryBo queryBo = new FeederRangeQueryBo();
            queryBo.setPsrId(feederId);
            queryBo.setRange(radius);
            queryBo.setNum(-1); // 返回所有附近线路
            // 记录耗时时间
            long startTime = System.currentTimeMillis();
            // 查询附近线路
            List<NeedFeederVo> nearbyFeeders = feederRangeQuery(queryBo);
            log.info("查询附近线路耗时: {}ms", System.currentTimeMillis() - startTime);

            if (CollectionUtils.isEmpty(nearbyFeeders)) {
                log.info("未找到线路{}半径{}米内的附近线路", feederId, radius);
                return new ArrayList<>();
            }

            // 提取线路ID列表
            List<String> feederIds = nearbyFeeders.stream().map(NeedFeederVo::getPsrId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(feederIds)) {
                log.warn("附近线路ID列表为空");
                return new ArrayList<>();
            }

            // 查询线路负载率信息
            List<FeederNtVo> feederNtList = feederDeviceMapper.selectFeederNtsByFeederIds(feederIds);
            Map<String, FeederNtVo> feederNtMap = feederNtList.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, Function.identity(), (existing, replacement) -> existing));

            // 获取重过载阈值
            double maxLoadRate = contactHandleService.getMaxFeederLoad();

            // 构建结果列表
            List<NearbyLineInfoVo> result = new ArrayList<>();
            for (NeedFeederVo needFeeder : nearbyFeeders) {
                try {
                    NearbyLineInfoVo lineInfo = buildNearbyLineInfo(needFeeder, feederNtMap, maxLoadRate);
                    if (lineInfo != null) {
                        result.add(lineInfo);
                    }
                } catch (Exception e) {
                    log.error("线路{}的附近线路信息失败: {}", needFeeder.getPsrId(), e.getMessage(), e);
                }
            }

            log.info("成功查询到{}条附近线路信息", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询附近线路信息失败，线路ID: {}, 半径: {}, 异常: {}", feederId, radius, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建单个附近线路信息
     *
     * @param needFeeder  附近线路基础信息
     * @param feederNtMap 线路负载率信息映射
     * @param maxLoadRate 重过载阈值
     * @return 附近线路信息VO
     */
    private NearbyLineInfoVo buildNearbyLineInfo(NeedFeederVo needFeeder, Map<String, FeederNtVo> feederNtMap, double maxLoadRate) {
        if (needFeeder == null || StringUtils.isEmpty(needFeeder.getPsrId())) {
            return null;
        }

        try {
            NearbyLineInfoVo lineInfo = new NearbyLineInfoVo();

            // 设置基础信息
            lineInfo.setFeederId(needFeeder.getPsrId());
            lineInfo.setFeederName(needFeeder.getFeedName());
            lineInfo.setSupplyRadius(needFeeder.getSupplyRadius());

            // 设置负载率信息
            FeederNtVo feederNt = feederNtMap.get(needFeeder.getPsrId());
            if (feederNt != null) {
                double loadRateValue = feederNt.getHisMaxLoadRate();
                lineInfo.setLoadRateValue(loadRateValue);
                lineInfo.setLoadRate(feederNt.getHisMaxLoadRateStr());
                lineInfo.setIsOverload(loadRateValue >= maxLoadRate);
            } else {
                lineInfo.setLoadRateValue(0.0);
                lineInfo.setLoadRate("0.0");
                lineInfo.setIsOverload(false);
                log.warn("线路{}未找到负载率信息", needFeeder.getPsrId());
            }

            // 查询母线和隔离开关信息
            BusbarSwitchVo busbarSwitch = bayQueryService.queryLineTopology(needFeeder.getPsrId());
            if (busbarSwitch != null) {
                lineInfo.setBusbarId(busbarSwitch.getBusbarId());
                lineInfo.setBusbarName(busbarSwitch.getBusbarName());
                lineInfo.setSwitchId(busbarSwitch.getSwitchId());
                lineInfo.setSwitchName(busbarSwitch.getSwitchName());
                lineInfo.setSubstationId(busbarSwitch.getStationPsrId());
                lineInfo.setIsSpare(busbarSwitch.getIsSpare());
                lineInfo.setSubstationName(busbarSwitch.getStationPsrName());
            } else {
                log.warn("线路{}未找到母线和开关信息", needFeeder.getPsrId());
            }

            return lineInfo;
        } catch (Exception e) {
            log.error("构建线路{}信息失败: {}", needFeeder.getPsrId(), e.getMessage(), e);
            return null;
        }
    }


    /**
     * 根据线路ID查询附近变电站信息
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstations(String psrId) {
        return nearbyQueryService.queryNearbySubstations(psrId);
    }


    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstationsByNet(String feederId, double bufferRadius) {
        try {
            // 区分内网和外网  外网是模拟接口 参数不一样
            AmapSdkCommon.PSRByPolygonRspModel substationResult;
            if (bdzHandlerMode) {
                log.info("通过内网接口查询附近变电站信息，feederId: {}", feederId);
                // 获取坐标
                LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
                DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
                if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                    log.warn("未找到馈线{}的地理坐标", feederId);
                    return new ArrayList<>();
                }
                List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
                if (CollectionUtils.isEmpty(feederLineSegments)) {
                    log.warn("馈线{}坐标解析失败", feederId);
                    return new ArrayList<>();
                }
                // 创建缓冲区多边形
                List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(feederLineSegments, bufferRadius);
                // 判断首尾经纬度是否一致，不一致将头添加到尾部
                if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                    bufferPolygon.add(bufferPolygon.get(0));
                }
                // 转为墨卡托坐标
                String polygonMercator = BufferPolygonCreator.convertToMercator(bufferPolygon);
                // 查询附近变电站
                substationResult = querySubstationsByPolygon(polygonMercator);
            } else {
                log.info("外网模拟接口未查询到变电站数据");
                substationResult = querySubstationsByPolygonMock(feederId);
            }

            if (substationResult.getResult() == null || CollectionUtils.isEmpty(substationResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到变电站数据");
                return new ArrayList<>();
            }

            List<NearbySubstationInfoVo> result = processSubstationData(substationResult, feederId);
            log.info("通过内网接口成功查询到{}个变电站", result.size());
            return result;
        } catch (Exception e) {
            log.error("通过内网接口查询附近变电站失败，feederId: {}", feederId, e);
            return nearbyQueryService.queryNearbySubstations(feederId);
        }
    }

    private AmapSdkCommon.PSRByPolygonRspModel querySubstationsByPolygonMock(String feederId) throws IOException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("feederId", feederId);
        requestBody.put("type", "BDZ");
        return AmapSdkCommon.queryPSRByPolygon(requestBody);
    }

    /**
     * 通过多边形查询变电站信息
     *
     * @param polygonMercator 墨卡托坐标系的多边形字符串
     * @return 变电站查询结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel querySubstationsByPolygon(String polygonMercator) throws IOException {


        /*// 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygonMercator);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // 查询变电站（zf01类型，distribution=1）
        Map<String, Object> substationQuery = new HashMap<>();
        substationQuery.put("psrType", "zf01");
        substationQuery.put("whereClause", "1=1");
        substationQuery.put("attrNameList", null);
        substationQuery.put("distribution", 1);
        psrQueryList.add(substationQuery);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 需要返回的属性值
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("psrId");
        attrNameList.add("psrName");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("coordinate");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("zoneName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);*/
        // 需要查询psrType
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.add("zf01");

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 1).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName"));
        powerGridFilter.setPolygon(polygonMercator);
        powerGridFilter.setSrs("EPSG:3857");

        return AmapSdkCommon.queryPSRByPolygon(powerGridFilter);
    }

    /**
     * 处理变电站数据并补充其他信息
     *
     * @param substationResult 内网接口返回的变电站数据
     * @param feederId         馈线ID
     * @return 数据补充
     */
    private List<NearbySubstationInfoVo> processSubstationData(AmapSdkCommon.PSRByPolygonRspModel substationResult, String feederId) {
        List<NearbySubstationInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : substationResult.getResult().getPsrDataList()) {
                if (!"zf01".equals(psrDataList.getPsrType()) || CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbySubstationInfoVo substationInfo = convertToSubstationInfo(psrDevice);
                        if (substationInfo != null) {
                            result.add(substationInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理变电站{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理变电站数据失败", e);
        }
        return result;
    }

    /**
     * 将PSR设备信息转换为变电站信息VO
     *
     * @param psrDevice PSR设备信息
     * @return 变电站信息VO
     */
    private NearbySubstationInfoVo convertToSubstationInfo(AmapSdkCommon.PSRDevice psrDevice) {
        try {
            NearbySubstationInfoVo substationInfo = new NearbySubstationInfoVo();

            // 设置基础信息
            substationInfo.setPsrId(psrDevice.getPsrId());
            substationInfo.setName(psrDevice.getPsrName());

            String coordinate = psrDevice.getCoordinate();
            if (StringUtils.isNotBlank(coordinate)) {
                String[] coordinates = coordinate.split(" ");
                substationInfo.setLngLat(MercatorLineCircleIntersection.mercatorToLngLat(Double.parseDouble(coordinates[0]), Double.parseDouble(coordinates[1])));
            }
            substationInfo.setGeoPositon(coordinate);

            // 获取emsId，查询剩余间隔
            String emsId = findEmsIdByPsrId(psrDevice.getPsrId());

            // 查询剩余间隔信息
            if (StringUtils.isNotEmpty(emsId)) {
                List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(emsId);
                if (CollectionUtils.isNotEmpty(topoTraceReals)) {
                    // 过滤备用间隔
                    List<TopoTraceReal> remainingBays = topoTraceReals.stream().filter(t -> StringUtils.isNotBlank(t.getStartBreakName()) && (t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留"))).collect(Collectors.toList());
                    substationInfo.setRemainingBayCount(remainingBays.size());
                    // 母线开关信息
                    List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
                    for (TopoTraceReal topoTraceReal : remainingBays) {
                        BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
                        busbarSwitchVo.setStationPsrId(emsId);
                        busbarSwitchVo.setStationPsrName(psrDevice.getPsrName());
                        busbarSwitchVo.setBusbarId(topoTraceReal.getBusbar());
                        busbarSwitchVo.setBusbarName(topoTraceReal.getBusbarName());
                        busbarSwitchVo.setSwitchId(topoTraceReal.getStartBreak());
                        busbarSwitchVo.setSwitchName(topoTraceReal.getStartBreakName());
                        busbarSwitchVo.setIsSpare(true);
                        busbarSwitchVoList.add(busbarSwitchVo);
                    }
                    substationInfo.setBusbarSwitchVoList(busbarSwitchVoList);
                } else {
                    substationInfo.setRemainingBayCount(0);
                    substationInfo.setBusbarSwitchVoList(new ArrayList<>());
                }
            } else {
                substationInfo.setRemainingBayCount(0);
                substationInfo.setBusbarSwitchVoList(new ArrayList<>());
            }
            return substationInfo;
        } catch (Exception e) {
            log.error("转换变电站信息失败: {}", psrDevice.getPsrName(), e);
            return null;
        }
    }

    /**
     * 通过psrId查找对应的emsId
     *
     * @param psrId PSR设备ID
     * @return emsId，如果未找到返回null
     */
    private String findEmsIdByPsrId(String psrId) {
        try {
            LambdaQueryWrapper<DeviceSubstation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceSubstation::getPsrId, psrId);
            DeviceSubstation localSubstation = deviceSubstation2Mapper.selectOne(queryWrapper);

            if (localSubstation != null) {
                return localSubstation.getEmsId();
            }
            log.debug("未在本地数据库中找到psrId={}对应的变电站", psrId);
            return null;
        } catch (Exception e) {
            log.warn("查找emsId失败，psrId: {}", psrId, e);
            return psrId;
        }
    }

    @Override
    public List<NearbyDeviceInfoVo> queryNearbyDevices(String feederId, double bufferRadius) {
        try {
            AmapSdkCommon.PSRByPolygonRspModel deviceResult;
            if (bdzHandlerMode) {
                log.info("通过内网接口查询附近设备信息，feederId: {}", feederId);
                // 获取馈线的地理坐标信息
                LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
                DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

                if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                    log.warn("未找到馈线{}的地理坐标", feederId);
                    return new ArrayList<>();
                }
                // 解析馈线坐标并创建缓冲区
                List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
                if (CollectionUtils.isEmpty(feederLineSegments)) {
                    log.warn("馈线{}坐标解析失败", feederId);
                    return new ArrayList<>();
                }
                // 创建缓冲区多边形
                List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(feederLineSegments, bufferRadius);
                // 判断首尾经纬度是否一致，不一致将头添加到尾部
                if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                    bufferPolygon.add(bufferPolygon.get(0));
                }
                // 转换为墨卡托坐标
                String polygonMercator = BufferPolygonCreator.convertToMercator(bufferPolygon);
                // 调用内网接口查询设备
                deviceResult = queryDevicesByPolygon(polygonMercator);
            } else {
                log.info("调用外网模拟接口");
                deviceResult = queryDevicesByPolygonMock(feederId);
            }

            if (deviceResult.getResult() == null || CollectionUtils.isEmpty(deviceResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到设备数据");
                return new ArrayList<>();
            }
            // 解析设备数据
            List<NearbyDeviceInfoVo> result = processDeviceData(deviceResult, feederId);

            log.info("通过内网接口成功查询到{}个设备", result.size());
            return result;
        } catch (Exception e) {
            log.error("通过内网接口查询附近设备失败，feederId: {}", feederId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据一个坐标点查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     * 排除掉当前线路的设备。 剩余的设备根据当前坐标和返回设备的坐标计算距离，判断那条线上的负载率是否正常，不正常排除掉那条线上的设备 最终返回多条符合结果的数据，但是只有一条是最符合的，就是在符合结果集中取最近的，
     * @param lng 经度
     * @param lat 纬度
     * @param bufferRadius 查询半径（米）
     * @return 附近设备信息列表，按距离排序，第一个为最符合的设备
     */
    @Override
    public List<NearbyDeviceInfoVo> queryNearbyDevicesByPoint(double lng, double lat, double bufferRadius) {
        return queryNearbyDevicesByPoint(lng, lat, bufferRadius, null);
    }

    /**
     * 根据一个坐标点查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     * 排除掉当前线路的设备。 剩余的设备根据当前坐标和返回设备的坐标计算距离，判断那条线上的负载率是否正常，不正常排除掉那条线上的设备 最终返回多条符合结果的数据，但是只有一条是最符合的，就是在符合结果集中取最近的，
     * @param lng 经度
     * @param lat 纬度
     * @param bufferRadius 查询半径（米）
     * @param excludeFeederId 需要排除的线路ID，可为null
     * @return 附近设备信息列表，按距离排序，第一个为最符合的设备
     */
    public List<NearbyDeviceInfoVo> queryNearbyDevicesByPoint(double lng, double lat, double bufferRadius, String excludeFeederId) {
        try {
            log.info("开始查询坐标点({}, {})半径{}米内的附近设备", lng, lat, bufferRadius);

            // 1. 调用内网接口查询设备
            AmapSdkCommon.PSRByPolygonRspModel deviceResult = queryPSRByCircle(lng, lat, bufferRadius);

            if (deviceResult.getResult() == null || CollectionUtils.isEmpty(deviceResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到设备数据");
                return new ArrayList<>();
            }

            // 2. 解析设备数据并计算距离
            List<NearbyDeviceInfoVo> allDevices = processDeviceDataWithDistance(deviceResult, lng, lat);

            if (CollectionUtils.isEmpty(allDevices)) {
                log.info("未找到符合条件的设备");
                return new ArrayList<>();
            }

            // 3. 排除当前线路的设备
            List<NearbyDeviceInfoVo> filteredDevices = excludeCurrentLineDevices(allDevices, excludeFeederId);

            // 4. 过滤负载率异常的线路设备
            List<NearbyDeviceInfoVo> normalLoadDevices = filterByLoadRate(filteredDevices);

            // 5. 按距离排序，最近的设备排在第一位
            normalLoadDevices.sort((d1, d2) -> Double.compare(d1.getDistance(), d2.getDistance()));

            log.info("成功查询到{}个符合条件的设备，最近设备距离{}米",
                    normalLoadDevices.size(),
                    normalLoadDevices.isEmpty() ? 0 : normalLoadDevices.get(0).getDistance());

            return normalLoadDevices;

        } catch (Exception e) {
            log.error("查询附近设备失败，坐标点({}, {}), 半径{}米: {}", lng, lat, bufferRadius, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private AmapSdkCommon.PSRByPolygonRspModel queryPSRByCircle(double lng, double lat, double bufferRadius) throws IOException {
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.addAll(HWG_TYPES);
        psrTypeList.addAll(POLE_TYPES);
        psrTypeList.addAll(KGZ_TYPES);

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 0).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName", "feederId", "feederName"));
        powerGridFilter.setRadius(bufferRadius);
        // 经纬度转墨卡托
        Coordinate coordinate = MercatorLineCircleIntersection.wgs84ToMercator(lng, lat);
        powerGridFilter.setPoint(coordinate.x + " " + coordinate.y);
        powerGridFilter.setSrs("EPSG:3857");

        return AmapSdkCommon.queryPSRByCircle(powerGridFilter);
    }

    /**
     * 处理设备数据并计算距离
     *
     * @param deviceResult 内网接口返回的设备数据
     * @param targetLng    目标点经度
     * @param targetLat    目标点纬度
     * @return 处理后的设备信息列表，包含距离信息
     */
    private List<NearbyDeviceInfoVo> processDeviceDataWithDistance(AmapSdkCommon.PSRByPolygonRspModel deviceResult, double targetLng, double targetLat) {
        List<NearbyDeviceInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : deviceResult.getResult().getPsrDataList()) {
                if (CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbyDeviceInfoVo deviceInfo = convertToDeviceInfo(psrDevice);
                        if (deviceInfo != null) {
                            // 计算距离
                            double distance = calculateDistance(deviceInfo, targetLng, targetLat);
                            deviceInfo.setDistance(distance);
                            result.add(deviceInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理设备{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理设备数据失败", e);
        }
        return result;
    }

    /**
     * 计算设备与目标点的距离
     *
     * @param deviceInfo 设备信息
     * @param targetLng  目标点经度
     * @param targetLat  目标点纬度
     * @return 距离（米）
     */
    private double calculateDistance(NearbyDeviceInfoVo deviceInfo, double targetLng, double targetLat) {
        if (deviceInfo.getLngLat() == null || deviceInfo.getLngLat().length < 2) {
            return Double.MAX_VALUE;
        }

        double deviceLng = deviceInfo.getLngLat()[0];
        double deviceLat = deviceInfo.getLngLat()[1];

        // 使用Haversine公式计算距离
        return GeoDistanceCalculator.calculateDistance(targetLat, targetLng, deviceLat, deviceLng);
    }

    /**
     * 排除当前线路的设备
     *
     * @param devices         设备列表
     * @param excludeFeederId 需要排除的线路ID
     * @return 过滤后的设备列表
     */
    private List<NearbyDeviceInfoVo> excludeCurrentLineDevices(List<NearbyDeviceInfoVo> devices, String excludeFeederId) {
        if (StringUtils.isEmpty(excludeFeederId) || CollectionUtils.isEmpty(devices)) {
            return devices;
        }

        return devices.stream()
                .filter(device -> {
                    List<String> feederIds = device.getFeederIds();
                    // 如果设备没有关联线路信息，保留该设备
                    if (CollectionUtils.isEmpty(feederIds)) {
                        return true;
                    }
                    // 排除包含当前线路ID的设备
                    return !feederIds.contains(excludeFeederId);
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据负载率过滤设备，排除负载率异常的线路上的设备
     *
     * @param devices 设备列表
     * @return 过滤后的设备列表
     */
    private List<NearbyDeviceInfoVo> filterByLoadRate(List<NearbyDeviceInfoVo> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return devices;
        }

        try {
            // 获取最大负载率阈值
            double maxLoadRate = contactHandleService.getMaxFeederLoad();

            // 收集所有设备关联的线路ID
            Set<String> allFeederIds = devices.stream()
                    .flatMap(device -> device.getFeederIds().stream())
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());

            if (allFeederIds.isEmpty()) {
                log.info("设备列表中没有关联线路信息，返回所有设备");
                return devices;
            }

            // 查询线路负载率信息
            List<FeederNtVo> feederNtList = feederDeviceMapper.selectFeederNtsByFeederIds(new ArrayList<>(allFeederIds));

            // 过滤出负载率正常的线路ID
            Set<String> normalFeederIds = feederNtList.stream()
                    .filter(feederNt -> feederNt.getHisMaxLoadRate() != null && feederNt.getHisMaxLoadRate() < maxLoadRate)
                    .map(FeederNtVo::getPsrId)
                    .collect(Collectors.toSet());

            // 过滤设备：保留没有关联线路或关联正常负载率线路的设备
            return devices.stream()
                    .filter(device -> {
                        List<String> feederIds = device.getFeederIds();
                        // 如果设备没有关联线路信息，保留该设备
                        if (CollectionUtils.isEmpty(feederIds)) {
                            return true;
                        }
                        // 检查是否有至少一条关联线路的负载率正常
                        return feederIds.stream().anyMatch(normalFeederIds::contains);
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("根据负载率过滤设备失败: {}", e.getMessage(), e);
            // 出现异常时返回原始设备列表
            return devices;
        }
    }

    /**
     * 外网模拟查询设备
     *
     * @param feederId
     * @return
     */
    private AmapSdkCommon.PSRByPolygonRspModel queryDevicesByPolygonMock(String feederId) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("feederId", feederId);
            requestBody.put("type", "GHB");
            return AmapSdkCommon.queryPSRByPolygon(requestBody);
        } catch (Exception e) {
            log.error("调用外网模拟接口失败", e);
        }
        return null;
    }

    /**
     * 通过多边形查询设备信息
     *
     * @param polygonMercator 墨卡托坐标系的多边形字符串
     * @return 设备查询结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel queryDevicesByPolygon(String polygonMercator) throws IOException {
        // 构建请求体
       /* Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygonMercator);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // 查询开关站（zf04类型）
        Map<String, Object> switchStationQuery = new HashMap<>();
        switchStationQuery.put("psrType", "zf04");
        switchStationQuery.put("whereClause", "1=1");
        switchStationQuery.put("attrNameList", null);
        switchStationQuery.put("distribution", 0);
        psrQueryList.add(switchStationQuery);

        // 查询环网柜（zf07类型）
        Map<String, Object> ringNetworkCabinetQuery = new HashMap<>();
        ringNetworkCabinetQuery.put("psrType", "zf07");
        ringNetworkCabinetQuery.put("whereClause", "1=1");
        ringNetworkCabinetQuery.put("attrNameList", null);
        ringNetworkCabinetQuery.put("distribution", 0);
        psrQueryList.add(ringNetworkCabinetQuery);

        // 查询物理杆塔（wlgt类型）
        Map<String, Object> physicalTowerQuery = new HashMap<>();
        physicalTowerQuery.put("psrType", "wlgt");
        physicalTowerQuery.put("whereClause", "1=1");
        physicalTowerQuery.put("attrNameList", null);
        physicalTowerQuery.put("distribution", 0);
        psrQueryList.add(physicalTowerQuery);

        // 查询运行杆塔（0103类型）
        Map<String, Object> runningTowerQuery = new HashMap<>();
        runningTowerQuery.put("psrType", "0103");
        runningTowerQuery.put("whereClause", "1=1");
        runningTowerQuery.put("attrNameList", null);
        runningTowerQuery.put("distribution", 0);
        psrQueryList.add(runningTowerQuery);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 设置需要返回的属性
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("psrId");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("psrName");
        attrNameList.add("zoneId");
        attrNameList.add("zoneName");
        attrNameList.add("portList");
        attrNameList.add("portNameList");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("coordinate");
        attrNameList.add("feederId");
        attrNameList.add("feederName");
        attrNameList.add("chargedState");
        attrNameList.add("switchStatus");
        attrNameList.add("distribution");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("crossFeederId");
        attrNameList.add("crossFeederProvince");
        attrNameList.add("isProvinceContainer");
        attrNameList.add("direction");
        attrNameList.add("siteName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);*/

        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.addAll(HWG_TYPES);
        psrTypeList.addAll(POLE_TYPES);
        psrTypeList.addAll(KGZ_TYPES);

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 0).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName"));
        powerGridFilter.setPolygon(polygonMercator);
        powerGridFilter.setSrs("EPSG:3857");

        return AmapSdkCommon.queryPSRByPolygon(powerGridFilter);
    }

    /**
     * 处理设备数据
     *
     * @param deviceResult 内网接口返回的设备数据
     * @param feederId     馈线ID
     * @return 处理后的设备信息列表
     */
    private List<NearbyDeviceInfoVo> processDeviceData(AmapSdkCommon.PSRByPolygonRspModel deviceResult, String feederId) {
        List<NearbyDeviceInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : deviceResult.getResult().getPsrDataList()) {
                if (CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbyDeviceInfoVo deviceInfo = convertToDeviceInfo(psrDevice);
                        if (deviceInfo != null) {
                            result.add(deviceInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理设备{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理设备数据失败", e);
        }
        return result;
    }

    /**
     * 将PSR设备信息转换为设备信息VO
     *
     * @param psrDevice PSR设备信息
     * @return 设备信息VO
     */
    private NearbyDeviceInfoVo convertToDeviceInfo(AmapSdkCommon.PSRDevice psrDevice) {
        try {
            NearbyDeviceInfoVo deviceInfo = new NearbyDeviceInfoVo();
            // 设置基础信息
            deviceInfo.setPsrId(psrDevice.getPsrId());
            deviceInfo.setPsrType(psrDevice.getPsrType());
            deviceInfo.setPsrTypeName(psrDevice.getPsrTypeName());
            deviceInfo.setPsrName(psrDevice.getPsrName());
            deviceInfo.setZoneId(psrDevice.getZoneId());
            deviceInfo.setZoneName(psrDevice.getZoneName());
            deviceInfo.setVlevelName(psrDevice.getVlevelName());
            deviceInfo.setVlevelCode(psrDevice.getVlevelCode());

            String coordinate = psrDevice.getCoordinate();
            if (StringUtils.isNotBlank(coordinate)) {
                String[] coordinates = coordinate.split(" ");
                deviceInfo.setLngLat(MercatorLineCircleIntersection.mercatorToLngLat(Double.parseDouble(coordinates[0]), Double.parseDouble(coordinates[1])));
            }
            deviceInfo.setCoordinate(coordinate);

            deviceInfo.setFeederId(psrDevice.getFeederId());
            deviceInfo.setFeederName(psrDevice.getFeederName());
            deviceInfo.setChargedState(psrDevice.getChargedState());
            deviceInfo.setSwitchStatus(psrDevice.getSwitchStatus());
            deviceInfo.setDistribution(psrDevice.getDistribution());
            deviceInfo.setMaintCrew(psrDevice.getMaintCrew());
            deviceInfo.setMaintCrewName(psrDevice.getMaintCrewName());
            deviceInfo.setMaintOrg(psrDevice.getMaintOrg());
            deviceInfo.setMaintOrgName(psrDevice.getMaintOrgName());
            deviceInfo.setCityOrg(psrDevice.getCityOrg());
            deviceInfo.setCityOrgName(psrDevice.getCityOrgName());
            deviceInfo.setProvinceId(psrDevice.getProvinceId());
            deviceInfo.setCrossFeederId(psrDevice.getCrossfeederid());
            deviceInfo.setCrossFeederProvince(psrDevice.getCrossfeederprovince());
            deviceInfo.setIsProvinceContainer(psrDevice.getIsprovincecontainer());
            deviceInfo.setDirection(psrDevice.getDirection());
            deviceInfo.setSiteName(psrDevice.getSiteName());

            // 对于环网柜和开关站，查询剩余间隔信息
            if (HWG_TYPES.contains(psrDevice.getPsrType()) || KGZ_TYPES.contains(psrDevice.getPsrType())) {
                queryRemainingBayInfo(deviceInfo, psrDevice);
            } else {
                // 其他设备类型不需要间隔信息
                deviceInfo.setRemainingBayCount(0);
                deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
            }

            return deviceInfo;

        } catch (Exception e) {
            log.error("转换设备信息失败: {}", psrDevice.getPsrName(), e);
            return null;
        }
    }

    /**
     * 查询环网柜和开关站的剩余间隔信息
     * TODO 这里后面查接口  不走本地的库了
     * @param deviceInfo 设备信息VO
     * @param psrDevice  PSR设备信息
     */
    private void queryRemainingBayInfo(NearbyDeviceInfoVo deviceInfo, AmapSdkCommon.PSRDevice psrDevice) {
        try {
            String psrId = deviceInfo.getPsrId();
            if (StringUtils.isNotEmpty(psrId)) {
                // 通过开关站/环网柜查剩余开关 这里你只查询了0305  0306那些都没查
                List<StationKg> stationKgs = kgMapper.selectStationKgByStationId(psrId);

                if (CollectionUtils.isNotEmpty(stationKgs)) {
                    // 查询剩余开关
                    List<StationKg> spareBreakerList = stationKgs.stream().filter(b -> b.getName().contains("备用") || b.getName().contains("预留")).collect(Collectors.toList());
                    // 构建开关信息
                    deviceInfo.setRemainingBayCount(spareBreakerList.size());
                    List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
                    for (StationKg breaker : spareBreakerList) {
                        BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
                        busbarSwitchVo.setFeederId(deviceInfo.getFeederId());
                        busbarSwitchVo.setFeederName(deviceInfo.getFeederName());
                        busbarSwitchVo.setSwitchId(breaker.getPsrId());
                        busbarSwitchVo.setSwitchType(breaker.getPsrType());
                        busbarSwitchVo.setSwitchName(breaker.getName());
                        busbarSwitchVo.setIsSpare(true);
                        busbarSwitchVo.setStationPsrType(deviceInfo.getPsrType());
                        busbarSwitchVoList.add(busbarSwitchVo);
                        deviceInfo.setBusbarSwitchVoList(busbarSwitchVoList);
                    }
                } else {
                    deviceInfo.setRemainingBayCount(0);
                    deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
                }
            } else {
                log.debug("未找到设备{}的psrId", psrDevice.getPsrId());
                deviceInfo.setRemainingBayCount(0);
                deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("查询设备{}剩余间隔信息失败: {}", psrDevice.getPsrId(), e.getMessage());
            deviceInfo.setRemainingBayCount(0);
            deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
        }
    }

}
